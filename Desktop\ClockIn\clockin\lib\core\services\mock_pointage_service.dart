import 'package:flutter/foundation.dart';
import '../models/worksite_model.dart';

class MockPointageService {
  static final MockPointageService _instance = MockPointageService._internal();
  factory MockPointageService() => _instance;
  MockPointageService._internal();

  final List<PointageData> _pointageHistory = [];
  PointageData? _currentPointage;

  /// Check if user location is within site range
  Future<LocationCheckResult> checkLocation({
    required String siteId,
    required double latitude,
    required double longitude,
  }) async {
    debugPrint('🎭 Mock: Checking location for site: $siteId');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 500));

    // Simuler différents scénarios
    final scenarios = [
      LocationCheckResult(
        isWithinRange: true,
        site: SiteInfo(id: siteId, name: 'Chantier Tour Eiffel'),
      ),
      LocationCheckResult(
        isWithinRange: false,
        site: SiteInfo(id: siteId, name: 'Chantier Tour Eiffel'),
      ),
    ];

    // Alterner entre les scénarios pour la démonstration
    final result = scenarios[DateTime.now().second % 2];
    
    debugPrint('✅ Mock: Location check result: ${result.isWithinRange}');
    return result;
  }

  /// Save pointage (clock in/out)
  Future<PointageResult> savePointage({
    required String siteId,
    required double latitude,
    required double longitude,
    required String type, // 'entree' or 'sortie'
    String? notes,
  }) async {
    debugPrint('🎭 Mock: Saving pointage - Type: $type, Site: $siteId');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(seconds: 1));

    final pointage = PointageData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'mock_user_1',
      siteId: siteId,
      type: type,
      latitude: latitude,
      longitude: longitude,
      createdAt: DateTime.now(),
      notes: notes,
    );

    _pointageHistory.add(pointage);
    
    if (type == 'entree') {
      _currentPointage = pointage;
    } else {
      _currentPointage = null;
    }

    debugPrint('✅ Mock: Pointage saved successfully');
    
    return PointageResult(
      success: true,
      message: type == 'entree' 
          ? 'Pointage d\'entrée enregistré avec succès'
          : 'Pointage de sortie enregistré avec succès',
      pointage: pointage,
    );
  }

  /// Get user's assigned sites
  Future<List<WorksiteModel>> getMySites() async {
    debugPrint('🎭 Mock: Getting my sites');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 600));

    final sites = [
      WorksiteModel(
        id: '1',
        name: 'Chantier Tour Eiffel',
        description: 'Rénovation de la structure métallique',
        latitude: 48.8584,
        longitude: 2.2945,
        radius: 100.0,
        address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris',
        type: WorksiteType.construction,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      WorksiteModel(
        id: '2',
        name: 'Bureau Défense',
        description: 'Aménagement des espaces de travail',
        latitude: 48.8922,
        longitude: 2.2358,
        radius: 50.0,
        address: '1 Parvis de la Défense, 92800 Puteaux',
        type: WorksiteType.office,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
    ];

    debugPrint('✅ Mock: Found ${sites.length} sites');
    return sites;
  }

  /// Get pointages (admin only)
  Future<List<PointageData>> getPointages({
    String? userId,
    String? siteId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    debugPrint('🎭 Mock: Getting pointages');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 800));

    // Générer des données de test si l'historique est vide
    if (_pointageHistory.isEmpty) {
      _generateTestPointages();
    }

    var filteredPointages = List<PointageData>.from(_pointageHistory);

    // Appliquer les filtres
    if (userId != null) {
      filteredPointages = filteredPointages.where((p) => p.userId == userId).toList();
    }
    if (siteId != null) {
      filteredPointages = filteredPointages.where((p) => p.siteId == siteId).toList();
    }
    if (startDate != null) {
      filteredPointages = filteredPointages.where((p) => p.createdAt.isAfter(startDate)).toList();
    }
    if (endDate != null) {
      filteredPointages = filteredPointages.where((p) => p.createdAt.isBefore(endDate)).toList();
    }

    debugPrint('✅ Mock: Found ${filteredPointages.length} pointages');
    return filteredPointages;
  }

  /// Get current pointage status
  PointageData? getCurrentPointage() {
    return _currentPointage;
  }

  /// Check if user is currently clocked in
  bool isCurrentlyClockedIn() {
    return _currentPointage != null;
  }

  /// Generate test pointages for demonstration
  void _generateTestPointages() {
    final now = DateTime.now();
    
    for (int i = 0; i < 10; i++) {
      final date = now.subtract(Duration(days: i));
      
      // Pointage d'entrée
      _pointageHistory.add(PointageData(
        id: 'entry_$i',
        userId: 'mock_user_1',
        siteId: i % 2 == 0 ? '1' : '2',
        type: 'entree',
        latitude: 48.8584 + (i * 0.001),
        longitude: 2.2945 + (i * 0.001),
        createdAt: DateTime(date.year, date.month, date.day, 8, 30 + i),
        notes: i % 3 == 0 ? 'Arrivée en avance' : null,
      ));

      // Pointage de sortie
      _pointageHistory.add(PointageData(
        id: 'exit_$i',
        userId: 'mock_user_1',
        siteId: i % 2 == 0 ? '1' : '2',
        type: 'sortie',
        latitude: 48.8584 + (i * 0.001),
        longitude: 2.2945 + (i * 0.001),
        createdAt: DateTime(date.year, date.month, date.day, 17, 30 + i),
        notes: i % 4 == 0 ? 'Heures supplémentaires' : null,
      ));
    }
  }

  /// Clear all data
  void clearData() {
    _pointageHistory.clear();
    _currentPointage = null;
    debugPrint('🎭 Mock: All pointage data cleared');
  }

  // Getters pour les tests
  List<PointageData> get pointageHistory => List.unmodifiable(_pointageHistory);
}

class LocationCheckResult {
  final bool isWithinRange;
  final SiteInfo? site;

  LocationCheckResult({
    required this.isWithinRange,
    this.site,
  });
}

class SiteInfo {
  final String id;
  final String name;

  SiteInfo({
    required this.id,
    required this.name,
  });

  factory SiteInfo.fromJson(Map<String, dynamic> json) {
    return SiteInfo(
      id: json['id'].toString(),
      name: json['name'] ?? '',
    );
  }
}

class PointageResult {
  final bool success;
  final String message;
  final PointageData? pointage;

  PointageResult({
    required this.success,
    required this.message,
    this.pointage,
  });
}

class PointageData {
  final String id;
  final String userId;
  final String siteId;
  final String type;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final String? notes;

  PointageData({
    required this.id,
    required this.userId,
    required this.siteId,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    this.notes,
  });

  factory PointageData.fromJson(Map<String, dynamic> json) {
    return PointageData(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      siteId: json['site_id'].toString(),
      type: json['type'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'site_id': siteId,
      'type': type,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
    };
  }
}
