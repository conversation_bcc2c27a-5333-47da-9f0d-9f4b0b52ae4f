import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/mock_auth_service.dart';

// Provider pour le service d'authentification mock
final mockAuthServiceProvider = Provider<MockAuthService>((ref) {
  return MockAuthService();
});

// Provider pour l'état d'authentification
final mockAuthProvider = StateNotifierProvider<MockAuthNotifier, MockAuthState>((ref) {
  final authService = ref.watch(mockAuthServiceProvider);
  return MockAuthNotifier(authService);
});

class MockAuthNotifier extends StateNotifier<MockAuthState> {
  final MockAuthService _authService;

  MockAuthNotifier(this._authService) : super(const MockAuthState.initial()) {
    _initialize();
  }

  Future<void> _initialize() async {
    state = const MockAuthState.loading();
    
    try {
      await _authService.initialize();
      
      if (_authService.isAuthenticated) {
        state = MockAuthState.authenticated(_authService.currentUser!);
      } else {
        state = const MockAuthState.unauthenticated();
      }
    } catch (e) {
      state = MockAuthState.error('Erreur d\'initialisation: $e');
    }
  }

  Future<void> login(String email, String password) async {
    state = const MockAuthState.loading();
    
    try {
      final result = await _authService.login(email, password);
      
      if (result.isSuccess) {
        state = MockAuthState.authenticated(result.user!);
      } else {
        state = MockAuthState.error(result.message ?? 'Erreur de connexion');
      }
    } catch (e) {
      state = MockAuthState.error('Erreur de connexion: $e');
    }
  }

  Future<void> logout() async {
    state = const MockAuthState.loading();
    
    try {
      await _authService.logout();
      state = const MockAuthState.unauthenticated();
    } catch (e) {
      state = MockAuthState.error('Erreur de déconnexion: $e');
    }
  }

  Future<void> validateToken() async {
    if (!_authService.isAuthenticated) {
      state = const MockAuthState.unauthenticated();
      return;
    }

    try {
      final isValid = await _authService.validateToken();
      
      if (isValid) {
        state = MockAuthState.authenticated(_authService.currentUser!);
      } else {
        state = const MockAuthState.unauthenticated();
      }
    } catch (e) {
      state = const MockAuthState.unauthenticated();
    }
  }

  Future<void> refreshToken() async {
    try {
      final result = await _authService.refreshToken();
      
      if (result.isSuccess) {
        state = MockAuthState.authenticated(result.user!);
      } else {
        state = const MockAuthState.unauthenticated();
      }
    } catch (e) {
      state = const MockAuthState.unauthenticated();
    }
  }

  // Méthodes utilitaires pour les tests
  void setTestUser(UserModel user) {
    _authService.setCurrentUser(user);
    state = MockAuthState.authenticated(user);
  }

  List<UserModel> get testUsers => _authService.testUsers;
}

// État d'authentification
class MockAuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final UserModel? user;
  final String? error;

  const MockAuthState._({
    required this.isLoading,
    required this.isAuthenticated,
    this.user,
    this.error,
  });

  const MockAuthState.initial()
      : isLoading = false,
        isAuthenticated = false,
        user = null,
        error = null;

  const MockAuthState.loading()
      : isLoading = true,
        isAuthenticated = false,
        user = null,
        error = null;

  const MockAuthState.authenticated(UserModel user)
      : isLoading = false,
        isAuthenticated = true,
        user = user,
        error = null;

  const MockAuthState.unauthenticated()
      : isLoading = false,
        isAuthenticated = false,
        user = null,
        error = null;

  const MockAuthState.error(String error)
      : isLoading = false,
        isAuthenticated = false,
        user = null,
        error = error;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MockAuthState &&
        other.isLoading == isLoading &&
        other.isAuthenticated == isAuthenticated &&
        other.user == user &&
        other.error == error;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        isAuthenticated.hashCode ^
        user.hashCode ^
        error.hashCode;
  }

  @override
  String toString() {
    return 'MockAuthState(isLoading: $isLoading, isAuthenticated: $isAuthenticated, user: $user, error: $error)';
  }
}
