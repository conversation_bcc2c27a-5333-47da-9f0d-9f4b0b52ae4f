import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';

class MockAuthService {
  static final MockAuthService _instance = MockAuthService._internal();
  factory MockAuthService() => _instance;
  MockAuthService._internal();

  UserModel? _currentUser;
  String? _authToken;

  UserModel? get currentUser => _currentUser;
  String? get authToken => _authToken;
  bool get isAuthenticated => _currentUser != null;

  // Utilisateurs de test
  final List<UserModel> _testUsers = [
    UserModel(
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: UserRole.employee,
      phoneNumber: '+33123456789',
      department: 'Construction',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),
    UserModel(
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: UserRole.admin,
      phoneNumber: '+33987654321',
      department: 'Administration',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
    ),
    UserModel(
      id: '3',
      name: 'Mohamed Alami',
      email: '<EMAIL>',
      role: UserRole.supervisor,
      phoneNumber: '+33555666777',
      department: 'Supervision',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    ),
  ];

  Future<void> initialize() async {
    await _loadStoredAuth();
  }

  Future<AuthResult> login(String email, String password) async {
    try {
      debugPrint('🎭 Mock Login: $email');
      
      // Simuler un délai réseau
      await Future.delayed(const Duration(seconds: 1));

      // Chercher l'utilisateur dans les données de test
      final user = _testUsers.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );

      // Simuler la validation du mot de passe (accepter n'importe quel mot de passe)
      if (password.isEmpty) {
        return AuthResult.failure('Le mot de passe est requis');
      }

      // Simuler la génération d'un token
      _authToken = 'mock_token_${user.id}_${DateTime.now().millisecondsSinceEpoch}';
      _currentUser = user;

      // Stocker les données d'authentification
      await _storeAuthData();

      debugPrint('✅ Mock Login Success: ${user.name}');
      return AuthResult.success(user);
    } catch (e) {
      debugPrint('❌ Mock Login Error: $e');
      return AuthResult.failure('Email ou mot de passe incorrect');
    }
  }

  Future<void> logout() async {
    debugPrint('🎭 Mock Logout');
    await _clearAuth();
  }

  Future<bool> validateToken() async {
    debugPrint('🎭 Mock Token Validation');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Toujours valide en mode mock
    return isAuthenticated;
  }

  Future<AuthResult> refreshToken() async {
    debugPrint('🎭 Mock Token Refresh');
    
    if (!isAuthenticated) {
      return AuthResult.failure('Non authentifié');
    }

    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 500));

    // Générer un nouveau token
    _authToken = 'mock_token_${_currentUser!.id}_${DateTime.now().millisecondsSinceEpoch}';
    await _storeAuthData();

    return AuthResult.success(_currentUser!);
  }

  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConstants.userTokenKey);
      final userJson = prefs.getString(AppConstants.userDataKey);

      if (token != null && userJson != null) {
        _authToken = token;
        // En mode mock, on peut recréer l'utilisateur ou utiliser les données stockées
        final userData = UserModel.fromJson({'id': '1', 'name': 'Mock User', 'email': '<EMAIL>'});
        _currentUser = userData;
        debugPrint('🎭 Mock Auth Loaded from Storage');
      }
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
    }
  }

  Future<void> _storeAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userTokenKey, _authToken!);
      await prefs.setString(AppConstants.userDataKey, _currentUser!.toJson().toString());
      debugPrint('🎭 Mock Auth Stored');
    } catch (e) {
      debugPrint('Error storing auth data: $e');
    }
  }

  Future<void> _clearAuth() async {
    _currentUser = null;
    _authToken = null;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userTokenKey);
      await prefs.remove(AppConstants.userDataKey);
      debugPrint('🎭 Mock Auth Cleared');
    } catch (e) {
      debugPrint('Error clearing auth: $e');
    }
  }

  // Méthodes utilitaires pour les tests
  List<UserModel> get testUsers => List.unmodifiable(_testUsers);
  
  void setCurrentUser(UserModel user) {
    _currentUser = user;
    _authToken = 'mock_token_${user.id}';
  }
}

class AuthResult {
  final bool isSuccess;
  final String? message;
  final UserModel? user;

  AuthResult._({
    required this.isSuccess,
    this.message,
    this.user,
  });

  factory AuthResult.success(UserModel user) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      message: 'Connexion réussie',
    );
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(
      isSuccess: false,
      message: message,
    );
  }
}
