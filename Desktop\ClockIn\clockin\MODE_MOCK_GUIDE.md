# Guide du Mode Mock - ClockIn

## 🎭 Qu'est-ce que le Mode Mock ?

Le mode mock permet de faire fonctionner l'application ClockIn **sans aucun backend** en utilisant des données simulées. C'est parfait pour :

- ✅ **Tester l'interface utilisateur** sans serveur
- ✅ **Développer les fonctionnalités** frontend
- ✅ **Démonstrations** et présentations
- ✅ **Tests d'interface** sur différents appareils

## 🔧 Activation du Mode Mock

### Configuration Actuelle
Le mode mock est **ACTIVÉ** par défaut dans `lib/core/config/app_config.dart` :

```dart
static const bool useMockServices = true; // ✅ Mode Mock ACTIVÉ
```

### Pour Désactiver le Mode Mock
Changez la valeur à `false` pour utiliser le vrai backend :

```dart
static const bool useMockServices = false; // ❌ Mode Mock DÉSACTIVÉ
```

## 🎯 Fonctionnalités Disponibles en Mode Mock

### ✅ Authentification
- **Connexion** avec n'importe quel mot de passe
- **Comptes de test** prédéfinis
- **Gestion des sessions** simulée
- **Validation des tokens** mock

### ✅ Gestion des Sites
- **Liste des sites** de travail simulés
- **Vérification de localisation** avec calculs réels
- **Création/modification** de sites (admin)
- **Assignation d'employés** aux sites

### ✅ Pointage
- **Clock-in/Clock-out** avec données simulées
- **Historique** des pointages
- **Vérification de géolocalisation** mock
- **Types de pointage** : entrée/sortie

### ✅ Géolocalisation
- **Positions de test** prédéfinies (Paris, La Défense, Roissy)
- **Calculs de distance** réels
- **Permissions** automatiquement accordées
- **Simulation de mouvement** entre les sites

## 👥 Comptes de Test Disponibles

### 1. Ahmed Benali (Employé)
```
Email: <EMAIL>
Mot de passe: n'importe lequel
Rôle: Employé
Département: Construction
```

### 2. Sarah Martin (Admin)
```
Email: <EMAIL>
Mot de passe: n'importe lequel
Rôle: Administrateur
Département: Administration
```

### 3. Mohamed Alami (Superviseur)
```
Email: <EMAIL>
Mot de passe: n'importe lequel
Rôle: Superviseur
Département: Supervision
```

## 🗺️ Sites de Test Disponibles

### 1. Chantier Tour Eiffel
- **Localisation** : 48.8584, 2.2945
- **Rayon** : 100m
- **Type** : Construction
- **Statut** : Actif

### 2. Bureau Défense
- **Localisation** : 48.8922, 2.2358
- **Rayon** : 50m
- **Type** : Bureau
- **Statut** : Actif

### 3. Entrepôt Roissy
- **Localisation** : 49.0097, 2.5479
- **Rayon** : 200m
- **Type** : Entrepôt
- **Statut** : Actif

## 🚀 Comment Utiliser le Mode Mock

### 1. Lancer l'Application
```bash
cd Desktop\ClockIn\clockin
flutter run
```

### 2. Connexion
1. **Ouvrir l'application**
2. **Voir l'indicateur orange** "MODE MOCK ACTIVÉ"
3. **Utiliser un des comptes de test** ou n'importe quel email
4. **Saisir n'importe quel mot de passe**
5. **Cliquer sur "Se connecter"**

### 3. Navigation
- **Dashboard employé** : Interface principale
- **Pointage** : Bouton central pour pointer
- **Historique** : Voir les pointages précédents
- **Profil** : Informations utilisateur

### 4. Test du Pointage
1. **Cliquer sur le bouton de pointage**
2. **La géolocalisation** sera simulée automatiquement
3. **Vérification de proximité** avec les sites
4. **Enregistrement** du pointage

## 🎨 Indicateurs Visuels du Mode Mock

### Écran de Connexion
- **Bandeau orange** : "MODE MOCK ACTIVÉ"
- **Liste des comptes** de test disponibles
- **Instructions** pour les mots de passe

### Interface Générale
- **Logs console** : Préfixés par 🎭
- **Délais simulés** : Pour imiter les appels réseau
- **Données cohérentes** : Historique et états persistants

## 🔍 Logs et Débogage

### Console Flutter
Tous les appels mock sont loggés avec le préfixe 🎭 :

```
🎭 Mock Login: <EMAIL>
✅ Mock Login Success: Ahmed Benali
🎭 Mock: Getting my sites
✅ Mock: Found 2 sites
🎭 Mock: Checking location for site: 1
✅ Mock: Distance: 45.2m, Within radius: true
```

### Configuration des Logs
Dans `app_config.dart` :

```dart
static const bool enableMockLogs = true; // Afficher les logs mock
static const bool enableDetailedLogs = true; // Logs détaillés
```

## ⚙️ Configuration Avancée

### Délais Réseau Simulés
```dart
static const Duration mockNetworkDelay = Duration(milliseconds: 800);
```

### Positions de Test
```dart
static const Map<String, Map<String, double>> mockLocations = {
  'tourEiffel': {'latitude': 48.8584, 'longitude': 2.2945},
  'laDefense': {'latitude': 48.8922, 'longitude': 2.2358},
  'roissy': {'latitude': 49.0097, 'longitude': 2.5479},
};
```

### Permissions Mock
```dart
static const bool mockLocationPermissionGranted = true;
static const bool mockCameraPermissionGranted = true;
static const bool mockNotificationPermissionGranted = true;
```

## 🔄 Basculer Entre les Modes

### Mode Mock → Mode Production
1. **Modifier** `app_config.dart` : `useMockServices = false`
2. **Configurer** l'URL du backend
3. **Redémarrer** l'application
4. **Tester** la connectivité API

### Mode Production → Mode Mock
1. **Modifier** `app_config.dart` : `useMockServices = true`
2. **Redémarrer** l'application
3. **Utiliser** les comptes de test

## 📱 Tests Recommandés

### Interface Utilisateur
- [ ] Connexion avec différents comptes
- [ ] Navigation entre les écrans
- [ ] Animations et transitions
- [ ] Responsive design
- [ ] Support RTL (arabe)

### Fonctionnalités
- [ ] Pointage entrée/sortie
- [ ] Vérification de géolocalisation
- [ ] Historique des pointages
- [ ] Gestion des profils
- [ ] Changement de langue

### Scénarios d'Erreur
- [ ] Connexion avec email invalide
- [ ] Pointage hors zone
- [ ] Perte de connexion simulée
- [ ] Permissions refusées

## 🎯 Avantages du Mode Mock

### Pour les Développeurs
- **Développement rapide** sans dépendances backend
- **Tests d'interface** isolés
- **Données cohérentes** et prévisibles
- **Pas de configuration** serveur requise

### Pour les Tests
- **Scénarios reproductibles** à chaque fois
- **Données de test** complètes
- **Performance constante** (pas de latence réseau)
- **Tests offline** possibles

### Pour les Démonstrations
- **Fonctionnement garanti** sans internet
- **Données réalistes** mais contrôlées
- **Pas de risque** de problèmes serveur
- **Interface complète** visible

## 🚨 Limitations du Mode Mock

### Ce qui N'est PAS Testé
- ❌ **Connectivité réseau** réelle
- ❌ **Performance** du backend
- ❌ **Sécurité** et authentification
- ❌ **Synchronisation** des données
- ❌ **Gestion d'erreurs** serveur

### Données Temporaires
- ❌ **Persistance limitée** (redémarrage = reset)
- ❌ **Pas de synchronisation** entre appareils
- ❌ **Données fictives** uniquement

## 📝 Prochaines Étapes

1. **Tester toutes les interfaces** en mode mock
2. **Valider l'expérience utilisateur** complète
3. **Corriger les problèmes** d'interface
4. **Basculer en mode production** pour les tests backend
5. **Intégrer les vraies données** progressivement

Le mode mock vous permet de **voir et tester toutes les interfaces** sans aucune dépendance backend ! 🎉
