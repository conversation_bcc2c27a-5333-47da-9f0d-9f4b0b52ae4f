import 'package:flutter/foundation.dart';
import '../models/worksite_model.dart';

class MockWorksiteService {
  static final MockWorksiteService _instance = MockWorksiteService._internal();
  factory MockWorksiteService() => _instance;
  MockWorksiteService._internal();

  List<WorksiteModel> _nearbyWorksites = [];
  WorksiteModel? _currentWorksite;

  List<WorksiteModel> get nearbyWorksites => _nearbyWorksites;
  WorksiteModel? get currentWorksite => _currentWorksite;

  // Sites de test
  final List<WorksiteModel> _testWorksites = [
    WorksiteModel(
      id: '1',
      name: 'Chantier Tour Eiffel',
      description: 'Rénovation de la structure métallique',
      latitude: 48.8584,
      longitude: 2.2945,
      radius: 100.0,
      address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris',
      type: WorksiteType.construction,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),
    WorksiteModel(
      id: '2',
      name: 'Bureau Défense',
      description: 'Aménagement des espaces de travail',
      latitude: 48.8922,
      longitude: 2.2358,
      radius: 50.0,
      address: '1 Parvis de la Défense, 92800 Puteaux',
      type: WorksiteType.office,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    ),
    WorksiteModel(
      id: '3',
      name: 'Entrepôt Roissy',
      description: 'Construction d\'un nouveau centre logistique',
      latitude: 49.0097,
      longitude: 2.5479,
      radius: 200.0,
      address: 'Zone Industrielle, 95700 Roissy-en-France',
      type: WorksiteType.warehouse,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now(),
    ),
    WorksiteModel(
      id: '4',
      name: 'Chantier Invalides',
      description: 'Restauration du dôme des Invalides',
      latitude: 48.8566,
      longitude: 2.3122,
      radius: 75.0,
      address: '129 Rue de Grenelle, 75007 Paris',
      type: WorksiteType.construction,
      isActive: false,
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
    ),
  ];

  /// Get user's assigned sites
  Future<List<WorksiteModel>> getMySites() async {
    debugPrint('🎭 Mock: Getting my sites');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 800));

    // Retourner seulement les sites actifs
    _nearbyWorksites = _testWorksites.where((site) => site.isActive).toList();
    
    debugPrint('✅ Mock: Found ${_nearbyWorksites.length} sites');
    return _nearbyWorksites;
  }

  /// Get all sites (admin only)
  Future<List<WorksiteModel>> getAllSites({
    int? page,
    int? perPage,
    String? search,
  }) async {
    debugPrint('🎭 Mock: Getting all sites');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 600));

    var sites = List<WorksiteModel>.from(_testWorksites);

    // Appliquer la recherche si fournie
    if (search != null && search.isNotEmpty) {
      sites = sites.where((site) => 
        site.name.toLowerCase().contains(search.toLowerCase()) ||
        site.description.toLowerCase().contains(search.toLowerCase()) ||
        site.address.toLowerCase().contains(search.toLowerCase())
      ).toList();
    }

    // Appliquer la pagination si fournie
    if (page != null && perPage != null) {
      final startIndex = (page - 1) * perPage;
      final endIndex = startIndex + perPage;
      if (startIndex < sites.length) {
        sites = sites.sublist(
          startIndex, 
          endIndex > sites.length ? sites.length : endIndex
        );
      } else {
        sites = [];
      }
    }

    debugPrint('✅ Mock: Found ${sites.length} sites');
    return sites;
  }

  /// Get worksite by ID
  Future<WorksiteModel?> getWorksiteById(String id) async {
    debugPrint('🎭 Mock: Getting worksite by ID: $id');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 400));

    try {
      final worksite = _testWorksites.firstWhere((site) => site.id == id);
      debugPrint('✅ Mock: Found worksite: ${worksite.name}');
      return worksite;
    } catch (e) {
      debugPrint('❌ Mock: Worksite not found: $id');
      return null;
    }
  }

  /// Create new site (admin only)
  Future<WorksiteModel?> createSite({
    required String name,
    required String description,
    required double latitude,
    required double longitude,
    required double radius,
    required String address,
    List<String>? assignedEmployees,
  }) async {
    debugPrint('🎭 Mock: Creating new site: $name');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(seconds: 1));

    final newSite = WorksiteModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      latitude: latitude,
      longitude: longitude,
      radius: radius,
      address: address,
      type: WorksiteType.construction, // Par défaut
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _testWorksites.add(newSite);
    debugPrint('✅ Mock: Site created: ${newSite.name}');
    return newSite;
  }

  /// Check if user is within site radius
  Future<LocationCheckResult> checkLocation({
    required String siteId,
    required double latitude,
    required double longitude,
  }) async {
    debugPrint('🎭 Mock: Checking location for site: $siteId');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 500));

    final site = _testWorksites.firstWhere(
      (s) => s.id == siteId,
      orElse: () => throw Exception('Site not found'),
    );

    // Calculer la distance (formule simplifiée)
    final distance = _calculateDistance(
      latitude, longitude,
      site.latitude, site.longitude,
    );

    final withinRadius = distance <= site.radius;

    debugPrint('✅ Mock: Distance: ${distance.toStringAsFixed(1)}m, Within radius: $withinRadius');

    return LocationCheckResult(
      withinRadius: withinRadius,
      distance: distance,
      worksite: site,
    );
  }

  /// Assign site to employees (admin only)
  Future<bool> assignSite({
    required String siteId,
    required List<String> employeeIds,
    String? message,
  }) async {
    debugPrint('🎭 Mock: Assigning site $siteId to ${employeeIds.length} employees');
    
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 800));

    // En mode mock, toujours réussir
    debugPrint('✅ Mock: Site assignment successful');
    return true;
  }

  /// Set current worksite
  void setCurrentWorksite(WorksiteModel? worksite) {
    _currentWorksite = worksite;
    debugPrint('🎭 Mock: Current worksite set to: ${worksite?.name ?? 'None'}');
  }

  /// Clear cached data
  void clearCache() {
    _nearbyWorksites.clear();
    _currentWorksite = null;
    debugPrint('🎭 Mock: Cache cleared');
  }

  /// Calculate distance between two points (simplified)
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // Formule simplifiée pour la démonstration
    // En réalité, on utiliserait la formule de Haversine
    const double earthRadius = 6371000; // mètres
    final double dLat = (lat2 - lat1) * (3.14159 / 180);
    final double dLon = (lon2 - lon1) * (3.14159 / 180);
    
    final double a = (dLat / 2) * (dLat / 2) + (dLon / 2) * (dLon / 2);
    final double c = 2 * (a < 1 ? a : 1);
    
    return earthRadius * c;
  }

  // Méthodes utilitaires pour les tests
  List<WorksiteModel> get testWorksites => List.unmodifiable(_testWorksites);
  
  void addTestWorksite(WorksiteModel worksite) {
    _testWorksites.add(worksite);
  }
}

class LocationCheckResult {
  final bool withinRadius;
  final double distance;
  final WorksiteModel? worksite;

  LocationCheckResult({
    required this.withinRadius,
    required this.distance,
    this.worksite,
  });
}
