/// Configuration de l'application
class AppConfig {
  // Mode Mock - Définit si l'application utilise des services mock ou réels
  static const bool useMockServices = true; // Changez à false pour utiliser les vrais services
  
  // Configuration API (utilisée seulement si useMockServices = false)
  static const String apiBaseUrl = 'http://10.0.2.2:8081/api';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Configuration Mock
  static const bool enableMockLogs = true; // Afficher les logs des services mock
  static const Duration mockNetworkDelay = Duration(milliseconds: 800); // D<PERSON>lai simulé
  
  // Configuration de l'interface
  static const bool showDebugInfo = true; // Afficher les informations de debug
  static const bool enableTestButtons = true; // Afficher les boutons de test
  
  // Configuration de localisation mock
  static const bool useMockLocation = true; // Utiliser la géolocalisation mock
  
  // Données de test par défaut
  static const String defaultTestEmail = '<EMAIL>';
  static const String defaultTestPassword = 'password123';
  
  // Messages d'information
  static String get modeInfo {
    if (useMockServices) {
      return '🎭 MODE MOCK ACTIVÉ\n'
             'L\'application fonctionne sans backend.\n'
             'Toutes les données sont simulées.';
    } else {
      return '🌐 MODE PRODUCTION\n'
             'L\'application se connecte au backend réel.\n'
             'URL: $apiBaseUrl';
    }
  }
  
  // Vérifications de configuration
  static bool get isValidConfiguration {
    if (!useMockServices) {
      return apiBaseUrl.isNotEmpty;
    }
    return true;
  }
  
  // Configuration des fonctionnalités
  static const Map<String, bool> features = {
    'authentication': true,
    'timeTracking': true,
    'locationServices': true,
    'notifications': true,
    'adminPanel': true,
    'reports': true,
    'settings': true,
  };
  
  // Vérifier si une fonctionnalité est activée
  static bool isFeatureEnabled(String feature) {
    return features[feature] ?? false;
  }
  
  // Configuration des couleurs pour le mode mock
  static const Map<String, String> mockColors = {
    'primary': '#2196F3',
    'secondary': '#4CAF50',
    'accent': '#FF9800',
    'error': '#F44336',
    'warning': '#FF5722',
    'success': '#4CAF50',
  };
  
  // Configuration des animations
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // Configuration des notifications mock
  static const bool enableMockNotifications = true;
  static const Duration notificationDelay = Duration(seconds: 2);
  
  // Configuration de la géolocalisation mock
  static const Map<String, Map<String, double>> mockLocations = {
    'tourEiffel': {
      'latitude': 48.8584,
      'longitude': 2.2945,
    },
    'laDefense': {
      'latitude': 48.8922,
      'longitude': 2.2358,
    },
    'roissy': {
      'latitude': 49.0097,
      'longitude': 2.5479,
    },
  };
  
  // Configuration des permissions mock
  static const bool mockLocationPermissionGranted = true;
  static const bool mockCameraPermissionGranted = true;
  static const bool mockNotificationPermissionGranted = true;
  
  // Configuration du stockage local
  static const bool useMockStorage = true;
  static const String storagePrefix = 'clockin_mock_';
  
  // Configuration des logs
  static const bool enableDetailedLogs = true;
  static const bool logApiCalls = true;
  static const bool logUserActions = true;
  
  // Méthodes utilitaires
  static void printConfiguration() {
    print('📱 Configuration de l\'application ClockIn');
    print('═' * 50);
    print('Mode Mock: ${useMockServices ? "ACTIVÉ" : "DÉSACTIVÉ"}');
    if (!useMockServices) {
      print('URL API: $apiBaseUrl');
    }
    print('Géolocalisation Mock: ${useMockLocation ? "ACTIVÉ" : "DÉSACTIVÉ"}');
    print('Logs détaillés: ${enableDetailedLogs ? "ACTIVÉ" : "DÉSACTIVÉ"}');
    print('Informations de debug: ${showDebugInfo ? "ACTIVÉ" : "DÉSACTIVÉ"}');
    print('═' * 50);
    
    if (useMockServices) {
      print('🎭 ATTENTION: Application en mode MOCK');
      print('   • Aucune connexion backend requise');
      print('   • Données simulées uniquement');
      print('   • Parfait pour tester l\'interface');
    }
  }
  
  // Validation de la configuration
  static List<String> validateConfiguration() {
    final errors = <String>[];
    
    if (!useMockServices) {
      if (apiBaseUrl.isEmpty) {
        errors.add('URL API manquante');
      }
      if (!apiBaseUrl.startsWith('http')) {
        errors.add('URL API invalide');
      }
    }
    
    return errors;
  }
}
