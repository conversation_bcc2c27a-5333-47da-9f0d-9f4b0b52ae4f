import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/location_data.dart';

class MockLocationService {
  static final MockLocationService _instance = MockLocationService._internal();
  factory MockLocationService() => _instance;
  MockLocationService._internal();

  // Positions de test (Paris et environs)
  final List<Position> _testPositions = [
    Position(
      latitude: 48.8584, // Tour Eiffel
      longitude: 2.2945,
      timestamp: DateTime.now(),
      accuracy: 5.0,
      altitude: 30.0,
      altitudeAccuracy: 3.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 1.0,
      headingAccuracy: 1.0,
    ),
    Position(
      latitude: 48.8922, // La Défense
      longitude: 2.2358,
      timestamp: DateTime.now(),
      accuracy: 8.0,
      altitude: 35.0,
      altitudeAccuracy: 3.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 1.0,
      headingAccuracy: 1.0,
    ),
    Position(
      latitude: 49.0097, // Roissy
      longitude: 2.5479,
      timestamp: DateTime.now(),
      accuracy: 10.0,
      altitude: 120.0,
      altitudeAccuracy: 5.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 1.0,
      headingAccuracy: 1.0,
    ),
  ];

  int _currentPositionIndex = 0;

  /// Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    debugPrint('🎭 Mock: Checking location service status');
    await Future.delayed(const Duration(milliseconds: 200));
    
    // En mode mock, toujours activé
    debugPrint('✅ Mock: Location service enabled');
    return true;
  }

  /// Check location permissions
  Future<LocationPermission> checkPermission() async {
    debugPrint('🎭 Mock: Checking location permission');
    await Future.delayed(const Duration(milliseconds: 300));
    
    // En mode mock, toujours autorisé
    debugPrint('✅ Mock: Location permission granted');
    return LocationPermission.always;
  }

  /// Request location permissions
  Future<LocationPermission> requestPermission() async {
    debugPrint('🎭 Mock: Requesting location permission');
    await Future.delayed(const Duration(seconds: 1));
    
    // En mode mock, toujours accorder la permission
    debugPrint('✅ Mock: Location permission granted');
    return LocationPermission.always;
  }

  /// Get current position
  Future<Position> getCurrentPosition() async {
    debugPrint('🎭 Mock: Getting current position');
    
    // Simuler un délai de géolocalisation
    await Future.delayed(const Duration(seconds: 2));

    // Retourner une position de test
    final position = _testPositions[_currentPositionIndex % _testPositions.length];
    _currentPositionIndex++;

    debugPrint('✅ Mock: Position obtained - Lat: ${position.latitude}, Lng: ${position.longitude}');
    return position;
  }

  /// Get position stream (for real-time tracking)
  Stream<Position> getPositionStream() async* {
    debugPrint('🎭 Mock: Starting position stream');
    
    while (true) {
      await Future.delayed(const Duration(seconds: 5));
      
      final position = _testPositions[_currentPositionIndex % _testPositions.length];
      _currentPositionIndex++;
      
      debugPrint('🎭 Mock: Stream position - Lat: ${position.latitude}, Lng: ${position.longitude}');
      yield position;
    }
  }

  /// Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    debugPrint('🎭 Mock: Calculating distance');
    
    // Utiliser la vraie fonction de Geolocator pour le calcul
    final distance = Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );

    debugPrint('✅ Mock: Distance calculated: ${distance.toStringAsFixed(1)}m');
    return distance;
  }

  /// Check if user is within a specific radius of a location
  Future<LocationVerificationResult> verifyLocation({
    required double targetLatitude,
    required double targetLongitude,
    required double allowedRadius,
  }) async {
    debugPrint('🎭 Mock: Verifying location within ${allowedRadius}m radius');
    
    // Simuler un délai de vérification
    await Future.delayed(const Duration(milliseconds: 800));

    final currentPosition = await getCurrentPosition();
    
    final distance = calculateDistance(
      currentPosition.latitude,
      currentPosition.longitude,
      targetLatitude,
      targetLongitude,
    );

    final isWithinRadius = distance <= allowedRadius;

    debugPrint('✅ Mock: Location verification - Distance: ${distance.toStringAsFixed(1)}m, Within radius: $isWithinRadius');

    return LocationVerificationResult(
      isWithinRadius: isWithinRadius,
      distance: distance,
      currentPosition: LocationData(
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        accuracy: currentPosition.accuracy,
        timestamp: currentPosition.timestamp,
      ),
    );
  }

  /// Get location data with additional information
  Future<LocationData> getLocationData() async {
    debugPrint('🎭 Mock: Getting detailed location data');
    
    final position = await getCurrentPosition();
    
    return LocationData(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      timestamp: position.timestamp,
      altitude: position.altitude,
      speed: position.speed,
      heading: position.heading,
    );
  }

  /// Start background location tracking
  Future<void> startLocationTracking() async {
    debugPrint('🎭 Mock: Starting background location tracking');
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('✅ Mock: Background tracking started');
  }

  /// Stop background location tracking
  Future<void> stopLocationTracking() async {
    debugPrint('🎭 Mock: Stopping background location tracking');
    await Future.delayed(const Duration(milliseconds: 200));
    debugPrint('✅ Mock: Background tracking stopped');
  }

  /// Get last known position
  Future<Position?> getLastKnownPosition() async {
    debugPrint('🎭 Mock: Getting last known position');
    await Future.delayed(const Duration(milliseconds: 100));
    
    final position = _testPositions[0]; // Toujours retourner la première position
    debugPrint('✅ Mock: Last known position retrieved');
    return position;
  }

  /// Simulate different location scenarios for testing
  void setTestScenario(LocationTestScenario scenario) {
    debugPrint('🎭 Mock: Setting test scenario: ${scenario.name}');
    
    switch (scenario) {
      case LocationTestScenario.tourEiffel:
        _currentPositionIndex = 0;
        break;
      case LocationTestScenario.laDefense:
        _currentPositionIndex = 1;
        break;
      case LocationTestScenario.roissy:
        _currentPositionIndex = 2;
        break;
    }
  }

  /// Get available test positions
  List<Position> get testPositions => List.unmodifiable(_testPositions);
  
  /// Add a custom test position
  void addTestPosition(Position position) {
    _testPositions.add(position);
    debugPrint('🎭 Mock: Added custom test position');
  }
}

class LocationVerificationResult {
  final bool isWithinRadius;
  final double distance;
  final LocationData currentPosition;

  LocationVerificationResult({
    required this.isWithinRadius,
    required this.distance,
    required this.currentPosition,
  });
}

enum LocationTestScenario {
  tourEiffel,
  laDefense,
  roissy,
}

extension LocationTestScenarioExtension on LocationTestScenario {
  String get name {
    switch (this) {
      case LocationTestScenario.tourEiffel:
        return 'Tour Eiffel';
      case LocationTestScenario.laDefense:
        return 'La Défense';
      case LocationTestScenario.roissy:
        return 'Roissy';
    }
  }

  String get description {
    switch (this) {
      case LocationTestScenario.tourEiffel:
        return 'Champ de Mars, Paris 7ème';
      case LocationTestScenario.laDefense:
        return 'Quartier d\'affaires, Puteaux';
      case LocationTestScenario.roissy:
        return 'Zone aéroportuaire, Roissy-en-France';
    }
  }
}
