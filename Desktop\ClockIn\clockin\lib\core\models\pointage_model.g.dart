// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pointage_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PointageModelAdapter extends TypeAdapter<PointageModel> {
  @override
  final int typeId = 7;

  @override
  PointageModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PointageModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      siteId: fields[2] as String,
      type: fields[3] as String,
      latitude: fields[4] as double,
      longitude: fields[5] as double,
      timestamp: fields[6] as DateTime,
      notes: fields[7] as String?,
      siteName: fields[8] as String?,
      userName: fields[9] as String?,
      address: fields[10] as String?,
      isVerified: fields[11] as bool,
      verificationMethod: fields[12] as String?,
      verifiedAt: fields[13] as DateTime?,
      deviceInfo: fields[14] as String?,
      accuracy: fields[15] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, PointageModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.siteId)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.latitude)
      ..writeByte(5)
      ..write(obj.longitude)
      ..writeByte(6)
      ..write(obj.timestamp)
      ..writeByte(7)
      ..write(obj.notes)
      ..writeByte(8)
      ..write(obj.siteName)
      ..writeByte(9)
      ..write(obj.userName)
      ..writeByte(10)
      ..write(obj.address)
      ..writeByte(11)
      ..write(obj.isVerified)
      ..writeByte(12)
      ..write(obj.verificationMethod)
      ..writeByte(13)
      ..write(obj.verifiedAt)
      ..writeByte(14)
      ..write(obj.deviceInfo)
      ..writeByte(15)
      ..write(obj.accuracy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PointageModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
